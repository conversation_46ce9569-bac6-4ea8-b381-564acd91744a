{"name": "qaaqconnect-mobile", "version": "2.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"expo": "~50.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.4", "react-native-maps": "1.10.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "expo-location": "~16.5.5", "expo-camera": "~14.1.3", "@react-native-async-storage/async-storage": "1.21.0", "react-native-vector-icons": "^10.0.3", "axios": "^1.6.0", "@tanstack/react-query": "^5.0.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "expo-blur": "~12.9.2", "expo-linear-gradient": "~12.7.2", "react-native-paper": "^5.11.0", "react-native-elements": "^3.4.3", "react-native-modal": "^13.0.1", "expo-notifications": "~0.27.6", "expo-device": "~5.9.3", "expo-constants": "~15.4.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.1.3"}, "private": true}